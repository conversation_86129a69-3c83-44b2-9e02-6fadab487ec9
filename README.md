# 功能展示
用于在抖音直播间自动挂机领取福袋，只要手机不关机就能24小时无限刷福袋  
一个月即使运气非常差，中10来个奖品还是轻松的！  
部分奖品：  
![image](https://github.com/user-attachments/assets/53873f4b-b5fa-47df-8340-52c08991af48)  
# 使用方法
1.手机打开“手势导航”，开启【屏幕内三键导航】，把3个按键显示出来  
2.手机连接电脑，开启开发者选项，usb调试打开，电脑上CMD命令提示行中输入adb devices命令能找到设备ID  
3.手机页面初始化位置，打开抖音->个人中心->关注->直播中->随意进一个直播间  
4.然后执行douyin_guaji.py文件即可  
5.要是完全不知道咋弄或者运行后识别福袋内容有误，你可以到B站给叼哥充电，会帮你私有化部署
## 注意
6.缺少各种python三方库文件的需要自己安装一下，运行下面的命令：  
pip install pillow paddlepaddle paddleocr numpy  
从粉丝反馈来看，最新版的paddlepaddle paddleocr不一定好用，降版本反倒更好使  
7.根据自己设备的情况，修改douyin_guaji.py文件中y轴的像素偏移值，可正可负，通常调整区间在【-30,30】，极端的碰到过-48的  
默认手机分辨率是1080*2400的像素，即使是相同的分辨率，可能也会因为手机顶部的刘海不一样，以及是否使用了底部导航按钮，导致存在Y的偏移 
![image](https://github.com/user-attachments/assets/e99dbd11-137b-407f-a37f-92a7b8235f90)  
8.将会在4.0版本自动识别偏移值，不需要调整任何参数，全自动识别、自动运行（叼哥B站达到1.5W粉发布）  

通过电脑的画图软件就能看图标的像素位置   
关于判定福袋存不存在小图标  
![image](https://github.com/user-attachments/assets/f494242b-bd57-4969-bd95-eb4c77d8c39e)

# 看到最后了，白嫖成功记得点个星，或者回头给叼哥充个电进QQ群！
